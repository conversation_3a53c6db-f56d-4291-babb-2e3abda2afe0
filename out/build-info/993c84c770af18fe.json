{"id": "993c84c770af18fe", "source_id_to_path": {"0": "contracts/AbstractAMM.sol", "1": "contracts/KuruAMMVault.sol", "2": "contracts/KuruForwarder.sol", "3": "contracts/MarginAccount.sol", "4": "contracts/OrderBook.sol", "5": "contracts/Router.sol", "6": "contracts/interfaces/IKuruAMMVault.sol", "7": "contracts/interfaces/IMarginAccount.sol", "8": "contracts/interfaces/IOrderBook.sol", "9": "contracts/interfaces/IRouter.sol", "10": "contracts/libraries/BitMath.sol", "11": "contracts/libraries/ERC2771Context.sol", "12": "contracts/libraries/Errors.sol", "13": "contracts/libraries/FixedPointMathLib.sol", "14": "contracts/libraries/OrderLinkedList.sol", "15": "contracts/libraries/TreeMath.sol", "16": "lib/forge-std/src/Base.sol", "17": "lib/forge-std/src/StdAssertions.sol", "18": "lib/forge-std/src/StdChains.sol", "19": "lib/forge-std/src/StdCheats.sol", "20": "lib/forge-std/src/StdConstants.sol", "21": "lib/forge-std/src/StdError.sol", "22": "lib/forge-std/src/StdInvariant.sol", "23": "lib/forge-std/src/StdJson.sol", "24": "lib/forge-std/src/StdMath.sol", "25": "lib/forge-std/src/StdStorage.sol", "26": "lib/forge-std/src/StdStyle.sol", "27": "lib/forge-std/src/StdToml.sol", "28": "lib/forge-std/src/StdUtils.sol", "29": "lib/forge-std/src/Test.sol", "30": "lib/forge-std/src/Vm.sol", "31": "lib/forge-std/src/console.sol", "32": "lib/forge-std/src/console2.sol", "33": "lib/forge-std/src/interfaces/IMulticall3.sol", "34": "lib/forge-std/src/safeconsole.sol", "35": "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "36": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "37": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "38": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "39": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "40": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "41": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "42": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "43": "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "44": "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "45": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "46": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "47": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "48": "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "49": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "50": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "51": "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "52": "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "53": "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "54": "node_modules/@openzeppelin/contracts/utils/Context.sol", "55": "node_modules/solady/src/auth/Ownable.sol", "56": "node_modules/solady/src/tokens/ERC20.sol", "57": "node_modules/solady/src/utils/ECDSA.sol", "58": "node_modules/solady/src/utils/EIP712.sol", "59": "node_modules/solady/src/utils/Initializable.sol", "60": "node_modules/solady/src/utils/SafeTransferLib.sol", "61": "node_modules/solady/src/utils/UUPSUpgradeable.sol", "62": "test/Helper.sol", "63": "test/KuruForwarderTest.t.sol", "64": "test/lib/MintableERC20.sol"}, "language": "Solidity"}