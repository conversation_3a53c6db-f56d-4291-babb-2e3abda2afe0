{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "contracts", "tests": "test", "scripts": "script", "libraries": ["lib", "node_modules"]}, "files": {"contracts/AbstractAMM.sol": {"lastModificationDate": *************, "contentHash": "762414151fbebd96", "interfaceReprHash": null, "sourceName": "contracts/AbstractAMM.sol", "imports": ["contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/libraries/BitMath.sol", "contracts/libraries/Errors.sol", "contracts/libraries/FixedPointMathLib.sol", "contracts/libraries/OrderLinkedList.sol", "contracts/libraries/TreeMath.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol"], "versionRequirement": "^0.8.20", "artifacts": {"AbstractAMM": {"0.8.30": {"default": {"path": "AbstractAMM.sol/AbstractAMM.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/KuruAMMVault.sol": {"lastModificationDate": *************, "contentHash": "456dc3ced1f7d228", "interfaceReprHash": null, "sourceName": "contracts/KuruAMMVault.sol", "imports": ["contracts/interfaces/IKuruAMMVault.sol", "contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/libraries/Errors.sol", "contracts/libraries/FixedPointMathLib.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "node_modules/solady/src/tokens/ERC20.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/SafeTransferLib.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol"], "versionRequirement": "^0.8.20", "artifacts": {"KuruAMMVault": {"0.8.30": {"default": {"path": "KuruAMMVault.sol/KuruAMMVault.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/KuruForwarder.sol": {"lastModificationDate": *************, "contentHash": "6272567a4bc81d59", "interfaceReprHash": null, "sourceName": "contracts/KuruForwarder.sol", "imports": ["contracts/interfaces/IOrderBook.sol", "contracts/libraries/Errors.sol", "node_modules/solady/src/auth/Ownable.sol", "node_modules/solady/src/utils/ECDSA.sol", "node_modules/solady/src/utils/EIP712.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol"], "versionRequirement": "^0.8.20", "artifacts": {"KuruForwarder": {"0.8.30": {"default": {"path": "KuruForwarder.sol/KuruForwarder.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/MarginAccount.sol": {"lastModificationDate": *************, "contentHash": "de009b6f3618b135", "interfaceReprHash": null, "sourceName": "contracts/MarginAccount.sol", "imports": ["contracts/interfaces/IMarginAccount.sol", "contracts/libraries/ERC2771Context.sol", "contracts/libraries/Errors.sol", "node_modules/solady/src/auth/Ownable.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/SafeTransferLib.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol"], "versionRequirement": "^0.8.20", "artifacts": {"MarginAccount": {"0.8.30": {"default": {"path": "MarginAccount.sol/MarginAccount.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/OrderBook.sol": {"lastModificationDate": *************, "contentHash": "3493f705a7c55862", "interfaceReprHash": null, "sourceName": "contracts/OrderBook.sol", "imports": ["contracts/AbstractAMM.sol", "contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/libraries/BitMath.sol", "contracts/libraries/ERC2771Context.sol", "contracts/libraries/Errors.sol", "contracts/libraries/FixedPointMathLib.sol", "contracts/libraries/OrderLinkedList.sol", "contracts/libraries/TreeMath.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/SafeTransferLib.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol"], "versionRequirement": "^0.8.20", "artifacts": {"OrderBook": {"0.8.30": {"default": {"path": "OrderBook.sol/OrderBook.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/Router.sol": {"lastModificationDate": *************, "contentHash": "e1f47694b6eea971", "interfaceReprHash": null, "sourceName": "contracts/Router.sol", "imports": ["contracts/interfaces/IKuruAMMVault.sol", "contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/interfaces/IRouter.sol", "contracts/libraries/Errors.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "node_modules/solady/src/auth/Ownable.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/SafeTransferLib.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Router": {"0.8.30": {"default": {"path": "Router.sol/Router.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/interfaces/IKuruAMMVault.sol": {"lastModificationDate": *************, "contentHash": "1cc8d9e7bae2b05f", "interfaceReprHash": null, "sourceName": "contracts/interfaces/IKuruAMMVault.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"IKuruAMMVault": {"0.8.30": {"default": {"path": "IKuruAMMVault.sol/IKuruAMMVault.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/interfaces/IMarginAccount.sol": {"lastModificationDate": *************, "contentHash": "58687def2a8aa855", "interfaceReprHash": null, "sourceName": "contracts/interfaces/IMarginAccount.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"IMarginAccount": {"0.8.30": {"default": {"path": "IMarginAccount.sol/IMarginAccount.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/interfaces/IOrderBook.sol": {"lastModificationDate": *************, "contentHash": "d9e8826c9ce9232e", "interfaceReprHash": null, "sourceName": "contracts/interfaces/IOrderBook.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"IOrderBook": {"0.8.30": {"default": {"path": "IOrderBook.sol/IOrderBook.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/interfaces/IRouter.sol": {"lastModificationDate": *************, "contentHash": "6c53d31cec2ec173", "interfaceReprHash": null, "sourceName": "contracts/interfaces/IRouter.sol", "imports": ["contracts/interfaces/IOrderBook.sol"], "versionRequirement": "^0.8.20", "artifacts": {"IRouter": {"0.8.30": {"default": {"path": "IRouter.sol/IRouter.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/BitMath.sol": {"lastModificationDate": *************, "contentHash": "94e916160bb6c6b3", "interfaceReprHash": null, "sourceName": "contracts/libraries/BitMath.sol", "imports": [], "versionRequirement": "^0.8.10", "artifacts": {"BitMath": {"0.8.30": {"default": {"path": "BitMath.sol/BitMath.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/ERC2771Context.sol": {"lastModificationDate": *************, "contentHash": "0b4fd871dd35116c", "interfaceReprHash": null, "sourceName": "contracts/libraries/ERC2771Context.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"ERC2771Context": {"0.8.30": {"default": {"path": "ERC2771Context.sol/ERC2771Context.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/Errors.sol": {"lastModificationDate": *************, "contentHash": "a8ba18e0d3dc0fd1", "interfaceReprHash": null, "sourceName": "contracts/libraries/Errors.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"KuruAMMVaultErrors": {"0.8.30": {"default": {"path": "Errors.sol/KuruAMMVaultErrors.json", "build_id": "5e10664fcd846148"}}}, "KuruForwarderErrors": {"0.8.30": {"default": {"path": "Errors.sol/KuruForwarderErrors.json", "build_id": "5e10664fcd846148"}}}, "MarginAccountErrors": {"0.8.30": {"default": {"path": "Errors.sol/MarginAccountErrors.json", "build_id": "5e10664fcd846148"}}}, "OrderBookErrors": {"0.8.30": {"default": {"path": "Errors.sol/OrderBookErrors.json", "build_id": "5e10664fcd846148"}}}, "RouterErrors": {"0.8.30": {"default": {"path": "Errors.sol/RouterErrors.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/FixedPointMathLib.sol": {"lastModificationDate": *************, "contentHash": "9b54872e2363a07b", "interfaceReprHash": null, "sourceName": "contracts/libraries/FixedPointMathLib.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"FixedPointMathLib": {"0.8.30": {"default": {"path": "FixedPointMathLib.sol/FixedPointMathLib.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/OrderLinkedList.sol": {"lastModificationDate": *************, "contentHash": "ccabc14eff2e5a93", "interfaceReprHash": null, "sourceName": "contracts/libraries/OrderLinkedList.sol", "imports": [], "versionRequirement": "^0.8.10", "artifacts": {"OrderLinkedList": {"0.8.30": {"default": {"path": "OrderLinkedList.sol/OrderLinkedList.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/libraries/TreeMath.sol": {"lastModificationDate": *************, "contentHash": "ffafaba11c281577", "interfaceReprHash": null, "sourceName": "contracts/libraries/TreeMath.sol", "imports": ["contracts/libraries/BitMath.sol"], "versionRequirement": "^0.8.10", "artifacts": {"TreeMath": {"0.8.30": {"default": {"path": "TreeMath.sol/TreeMath.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/periphery/ERC20.sol": {"lastModificationDate": *************, "contentHash": "32dcfbd7d4e8b30f", "interfaceReprHash": null, "sourceName": "contracts/periphery/ERC20.sol", "imports": ["node_modules/solady/src/tokens/ERC20.sol"], "versionRequirement": "^0.8.20", "artifacts": {"KuruERC20": {"0.8.30": {"default": {"path": "ERC20.sol/KuruERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/periphery/KuruUtils.sol": {"lastModificationDate": *************, "contentHash": "fefcf6970d9784d4", "interfaceReprHash": null, "sourceName": "contracts/periphery/KuruUtils.sol", "imports": ["contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol"], "versionRequirement": "^0.8.20", "artifacts": {"KuruUtils": {"0.8.30": {"default": {"path": "KuruUtils.sol/KuruUtils.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "contracts/periphery/MonadDeployer.sol": {"lastModificationDate": *************, "contentHash": "ae3fe1548c400116", "interfaceReprHash": null, "sourceName": "contracts/periphery/MonadDeployer.sol", "imports": ["contracts/interfaces/IKuruAMMVault.sol", "contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/interfaces/IRouter.sol", "contracts/periphery/ERC20.sol", "node_modules/solady/src/auth/Ownable.sol", "node_modules/solady/src/tokens/ERC20.sol"], "versionRequirement": "^0.8.20", "artifacts": {"MonadDeployer": {"0.8.30": {"default": {"path": "MonadDeployer.sol/MonadDeployer.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Base.sol": {"lastModificationDate": *************, "contentHash": "b30affbf365427e2", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Base.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.30": {"default": {"path": "Base.sol/CommonBase.json", "build_id": "5e10664fcd846148"}}}, "ScriptBase": {"0.8.30": {"default": {"path": "Base.sol/ScriptBase.json", "build_id": "5e10664fcd846148"}}}, "TestBase": {"0.8.30": {"default": {"path": "Base.sol/TestBase.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": *************, "contentHash": "02aafa55c6c27fcf", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdAssertions.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.30": {"default": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": *************, "contentHash": "a40952ce0d242817", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdChains.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.30": {"default": {"path": "StdChains.sol/StdChains.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": *************, "contentHash": "30325e8cda32c7ae", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdCheats.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.30": {"default": {"path": "StdCheats.sol/StdCheats.json", "build_id": "5e10664fcd846148"}}}, "StdCheatsSafe": {"0.8.30": {"default": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdConstants.sol": {"lastModificationDate": *************, "contentHash": "23303eb7e922efe4", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdConstants.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdConstants": {"0.8.30": {"default": {"path": "StdConstants.sol/StdConstants.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": *************, "contentHash": "a1a86c7115e2cdf3", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdError.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.30": {"default": {"path": "StdError.sol/stdError.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": *************, "contentHash": "0111ef959dff6f54", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdInvariant.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.30": {"default": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": *************, "contentHash": "5fb1b35c8fb281fd", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdJson.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.30": {"default": {"path": "StdJson.sol/stdJson.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": *************, "contentHash": "72584abebada1e7a", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdMath.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.30": {"default": {"path": "StdMath.sol/stdMath.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": *************, "contentHash": "9a44dcb9bda3bfa9", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStorage.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.30": {"default": {"path": "StdStorage.sol/stdStorage.json", "build_id": "5e10664fcd846148"}}}, "stdStorageSafe": {"0.8.30": {"default": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": *************, "contentHash": "ee166ef95092736e", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStyle.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.30": {"default": {"path": "StdStyle.sol/StdStyle.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdToml.sol": {"lastModificationDate": *************, "contentHash": "fc667e4ecb7fa86c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdToml.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdToml": {"0.8.30": {"default": {"path": "StdToml.sol/stdToml.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": *************, "contentHash": "b7cdeb66252de708", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdUtils.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.30": {"default": {"path": "StdUtils.sol/StdUtils.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": *************, "contentHash": "f56119a09f81c62c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Test.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.30": {"default": {"path": "Test.sol/Test.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1755598713141, "contentHash": "e42237c90542cb12", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Vm.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.30": {"default": {"path": "Vm.sol/Vm.json", "build_id": "5e10664fcd846148"}}}, "VmSafe": {"0.8.30": {"default": {"path": "Vm.sol/VmSafe.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1755598713073, "contentHash": "bae85493a76fb054", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console.sol", "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.30": {"default": {"path": "console.sol/console.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1755598713073, "contentHash": "49a7da3dfc404603", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console2.sol", "imports": ["lib/forge-std/src/console.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1755598713084, "contentHash": "b680a332ebf10901", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.30": {"default": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/forge-std/src/safeconsole.sol": {"lastModificationDate": 1755598713084, "contentHash": "621653b34a6691ea", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/safeconsole.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"safeconsole": {"0.8.30": {"default": {"path": "safeconsole.sol/safeconsole.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"lastModificationDate": 1755598731206, "contentHash": "ae7885d5bfccd2c9", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "imports": [], "versionRequirement": ">=0.4.11", "artifacts": {"IERC1967": {"0.8.30": {"default": {"path": "IERC1967.sol/IERC1967.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1755598731436, "contentHash": "9c740010cc7bb5db", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "imports": [], "versionRequirement": ">=0.8.4", "artifacts": {"IERC1155Errors": {"0.8.30": {"default": {"path": "draft-IERC6093.sol/IERC1155Errors.json", "build_id": "5e10664fcd846148"}}}, "IERC20Errors": {"0.8.30": {"default": {"path": "draft-IERC6093.sol/IERC20Errors.json", "build_id": "5e10664fcd846148"}}}, "IERC721Errors": {"0.8.30": {"default": {"path": "draft-IERC6093.sol/IERC721Errors.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"lastModificationDate": 1755598731237, "contentHash": "6f944b6db35e2072", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.22", "artifacts": {"ERC1967Proxy": {"0.8.30": {"default": {"path": "ERC1967Proxy.sol/ERC1967Proxy.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"lastModificationDate": 1755598731237, "contentHash": "4c17afdc9af158b0", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol"], "versionRequirement": "^0.8.21", "artifacts": {"ERC1967Utils": {"0.8.30": {"default": {"path": "ERC1967Utils.sol/ERC1967Utils.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"lastModificationDate": 1755598731237, "contentHash": "d6410a5092021245", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Proxy": {"0.8.30": {"default": {"path": "Proxy.sol/Proxy.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1755598731237, "contentHash": "ac349d9fb9a6fdba", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "imports": [], "versionRequirement": ">=0.4.16", "artifacts": {"IBeacon": {"0.8.30": {"default": {"path": "IBeacon.sol/IBeacon.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1755598731244, "contentHash": "93d784d4e49c0d24", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC20": {"0.8.30": {"default": {"path": "ERC20/ERC20.sol/ERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1755598731244, "contentHash": "1dcd768972ff31b3", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "imports": [], "versionRequirement": ">=0.4.16", "artifacts": {"IERC20": {"0.8.30": {"default": {"path": "IERC20.sol/IERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1755598731244, "contentHash": "c0fde354a75fbdc6", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "imports": ["lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC20Metadata": {"0.8.30": {"default": {"path": "IERC20Metadata.sol/IERC20Metadata.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"lastModificationDate": 1755598731252, "contentHash": "3a8447ab9fbdeb3c", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Errors.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Address": {"0.8.30": {"default": {"path": "Address.sol/Address.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"lastModificationDate": 1755598731252, "contentHash": "16db1f8b2f7183f5", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Context": {"0.8.30": {"default": {"path": "Context.sol/Context.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Create2.sol": {"lastModificationDate": 1755598731252, "contentHash": "1092bb43db4691fd", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Errors.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Create2": {"0.8.30": {"default": {"path": "Create2.sol/Create2.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"lastModificationDate": 1755598731252, "contentHash": "3c9245fed7a7e4ab", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Errors": {"0.8.30": {"default": {"path": "Errors.sol/Errors.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"lastModificationDate": 1755598731252, "contentHash": "cfb5098ef78673ff", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Panic": {"0.8.30": {"default": {"path": "Panic.sol/Panic.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"lastModificationDate": 1755598731252, "contentHash": "c32609055440f1b4", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol"], "versionRequirement": "^0.8.24", "artifacts": {"ReentrancyGuardTransient": {"0.8.30": {"default": {"path": "ReentrancyGuardTransient.sol/ReentrancyGuardTransient.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1755598731252, "contentHash": "261e9fcb6515866e", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"StorageSlot": {"0.8.30": {"default": {"path": "StorageSlot.sol/StorageSlot.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"lastModificationDate": 1755598731252, "contentHash": "25f0a4cf9ab0507c", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "imports": [], "versionRequirement": "^0.8.24", "artifacts": {"TransientSlot": {"0.8.30": {"default": {"path": "TransientSlot.sol/TransientSlot.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"lastModificationDate": 1755598731444, "contentHash": "f578cd1eb517fca5", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Math": {"0.8.30": {"default": {"path": "Math.sol/Math.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1755598731260, "contentHash": "5a907d9c96fd0da2", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"SafeCast": {"0.8.30": {"default": {"path": "SafeCast.sol/SafeCast.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1755598767373, "contentHash": "c0ab5169ca289ad9", "interfaceReprHash": null, "sourceName": "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "imports": ["node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "node_modules/@openzeppelin/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ERC20": {"0.8.30": {"default": {"path": "token/ERC20/ERC20.sol/ERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1755598769616, "contentHash": "93084c0063f8a256", "interfaceReprHash": null, "sourceName": "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IERC20": {"0.8.30": {"default": {"path": "ERC20/IERC20.sol/IERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1755598771257, "contentHash": "84c1a7303a6d24b7", "interfaceReprHash": null, "sourceName": "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "imports": ["node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"IERC20Metadata": {"0.8.30": {"default": {"path": "extensions/IERC20Metadata.sol/IERC20Metadata.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1755598766418, "contentHash": "7ebeeab55e6c0594", "interfaceReprHash": null, "sourceName": "node_modules/@openzeppelin/contracts/utils/Context.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Context": {"0.8.30": {"default": {"path": "utils/Context.sol/Context.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/auth/Ownable.sol": {"lastModificationDate": 1755598764459, "contentHash": "0ec6518942689f12", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/auth/Ownable.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"Ownable": {"0.8.30": {"default": {"path": "Ownable.sol/Ownable.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/tokens/ERC20.sol": {"lastModificationDate": 1755598761556, "contentHash": "d3dda4b5c064f1f0", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/tokens/ERC20.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"ERC20": {"0.8.30": {"default": {"path": "ERC20.sol/ERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/utils/ECDSA.sol": {"lastModificationDate": 1755598759634, "contentHash": "fdfb624aadc1074c", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/utils/ECDSA.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"ECDSA": {"0.8.30": {"default": {"path": "ECDSA.sol/ECDSA.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/utils/EIP712.sol": {"lastModificationDate": 1755598759919, "contentHash": "09350c2c86eb1552", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/utils/EIP712.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"EIP712": {"0.8.30": {"default": {"path": "EIP712.sol/EIP712.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/utils/Initializable.sol": {"lastModificationDate": 1755598762952, "contentHash": "e67c2ea34d4c61ac", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/utils/Initializable.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"Initializable": {"0.8.30": {"default": {"path": "Initializable.sol/Initializable.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"lastModificationDate": 1755598764788, "contentHash": "c7c98943a71ee26e", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/utils/SafeTransferLib.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"SafeTransferLib": {"0.8.30": {"default": {"path": "SafeTransferLib.sol/SafeTransferLib.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"lastModificationDate": 1755598765011, "contentHash": "15b6d67784300dae", "interfaceReprHash": null, "sourceName": "node_modules/solady/src/utils/UUPSUpgradeable.sol", "imports": [], "versionRequirement": "^0.8.4", "artifacts": {"UUPSUpgradeable": {"0.8.30": {"default": {"path": "UUPSUpgradeable.sol/UUPSUpgradeable.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "test/Helper.sol": {"lastModificationDate": 1755598565530, "contentHash": "fcc806f824dc5d5a", "interfaceReprHash": null, "sourceName": "test/Helper.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"PropertiesAsserts": {"0.8.30": {"default": {"path": "Helper.sol/PropertiesAsserts.json", "build_id": "5e10664fcd846148"}}}, "PropertiesLibString": {"0.8.30": {"default": {"path": "Helper.sol/PropertiesLibString.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}, "test/OrderBookTest.t.sol": {"lastModificationDate": *************, "contentHash": "fc26da563d30d1ae", "interfaceReprHash": null, "sourceName": "test/OrderBookTest.t.sol", "imports": ["contracts/AbstractAMM.sol", "contracts/KuruAMMVault.sol", "contracts/MarginAccount.sol", "contracts/OrderBook.sol", "contracts/Router.sol", "contracts/interfaces/IKuruAMMVault.sol", "contracts/interfaces/IMarginAccount.sol", "contracts/interfaces/IOrderBook.sol", "contracts/interfaces/IRouter.sol", "contracts/libraries/BitMath.sol", "contracts/libraries/ERC2771Context.sol", "contracts/libraries/Errors.sol", "contracts/libraries/FixedPointMathLib.sol", "contracts/libraries/OrderLinkedList.sol", "contracts/libraries/TreeMath.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "lib/openzeppelin-contracts/contracts/utils/Address.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Create2.sol", "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "node_modules/@openzeppelin/contracts/utils/Context.sol", "node_modules/solady/src/auth/Ownable.sol", "node_modules/solady/src/tokens/ERC20.sol", "node_modules/solady/src/utils/Initializable.sol", "node_modules/solady/src/utils/SafeTransferLib.sol", "node_modules/solady/src/utils/UUPSUpgradeable.sol", "test/Helper.sol", "test/lib/MintableERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"OrderBookTest": {"0.8.30": {"default": {"path": "OrderBookTest.t.sol/OrderBookTest.json", "build_id": "41a9e6a48eb72177"}}}}, "seenByCompiler": true}, "test/lib/MintableERC20.sol": {"lastModificationDate": 1755598565533, "contentHash": "4b9a9f299d55cb14", "interfaceReprHash": null, "sourceName": "test/lib/MintableERC20.sol", "imports": ["node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "node_modules/@openzeppelin/contracts/utils/Context.sol"], "versionRequirement": "^0.8.0", "artifacts": {"MintableERC20": {"0.8.30": {"default": {"path": "MintableERC20.sol/MintableERC20.json", "build_id": "5e10664fcd846148"}}}}, "seenByCompiler": true}}, "builds": ["41a9e6a48eb72177", "5e10664fcd846148"], "profiles": {"default": {"solc": {"optimizer": {"enabled": true, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode.object", "evm.bytecode.sourceMap", "evm.bytecode.linkReferences", "evm.deployedBytecode.object", "evm.deployedBytecode.sourceMap", "evm.deployedBytecode.linkReferences", "evm.deployedBytecode.immutableReferences", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "cancun", "viaIR": true, "libraries": {}}, "vyper": {"evmVersion": "cancun", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}}, "preprocessed": false, "mocks": []}